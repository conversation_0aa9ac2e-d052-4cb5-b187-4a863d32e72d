const { contextBridge, ipc<PERSON>enderer } = require('electron');

// 暴露安全的 API 给渲染进程
contextBridge.exposeInMainWorld('electronAPI', {
    // 通知主进程欢迎流程已完成
    completeWelcome: () => ipcRenderer.send('welcome-completed'),
    // 监听主进程的消息
    onMessage: (callback: (...args: any[]) => any) => ipcRenderer.on('message', callback),
    // 获取系统信息
    getSystemInfo: () => {
        return {
            platform: process.platform,
            version: process.versions.electron,
            isDev: process.env.NODE_ENV === 'development',
            isMacOS: process.platform === 'darwin',
        };
    },
    // 设置默认主题
    setTheme: (theme: string) => ipcRenderer.send('set-theme', theme),
    // 导入设置
    importConfig: (source: string) => ipcRenderer.invoke('import-config', source),
    // 安装命令
    installCLI: () => ipcRenderer.invoke('install-cli'),
    // 登录
    login: () => ipcRenderer.invoke('login'),
    // 检查是否安装了 cursor 或 vscode
    checkThirypartyIDEInstalled: (source: string) => ipcRenderer.invoke('check-ide-installed', source),
});
